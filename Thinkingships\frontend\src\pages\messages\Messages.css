.messages-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.messages-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: 1;
}

.messages-main {
  display: flex;
  width: 100%;
  height: 100vh;
  z-index: 2;
  position: relative;
}

/* Left Panel - Conversations */
.conversations-panel {
  width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.search-container {
  padding: 8px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 10px;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  animation: fadeInDown 0.8s ease-out 0.2s both;
  min-height: 40px;
}

@keyframes fadeInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.search-bar {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6c757d;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 14px;
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  border-color: #4A99F8;
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.3);
  transform: translateY(-2px);
}

.add-chat-btn {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4A99F8 0%, #667eea 100%);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(74, 153, 248, 0.4);
  position: relative;
  overflow: hidden;
}

.add-chat-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.add-chat-btn:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.6);
}

.add-chat-btn:hover::before {
  left: 100%;
}

.add-chat-btn:active {
  transform: translateY(0);
}

.conversations-list {
  flex: 1;
  overflow: hidden;
  padding: 0;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 40px;
  max-height: 40px;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out var(--delay, 0s) both;
}

.conversation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(8px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.conversation-item:hover::before {
  left: 100%;
}

.conversation-item.active {
  background: linear-gradient(135deg, rgba(74, 153, 248, 0.2) 0%, rgba(102, 126, 234, 0.2) 100%);
  border-right: 3px solid #4A99F8;
  transform: translateX(8px);
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.3);
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.conversation-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.conversation-item:hover .conversation-avatar {
  transform: scale(1.1);
  border-color: #4A99F8;
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.4);
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-name {
  font-weight: 600;
  font-size: 14px;
  color: #212529;
  margin-bottom: 4px;
}

.conversation-last-message {
  font-size: 13px;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 12px;
  color: #6c757d;
  margin-left: 8px;
}

/* Right Panel - Chat */
.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.6s ease-out 0.3s both;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.chat-header {
  padding: 8px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  animation: fadeInDown 0.8s ease-out 0.5s both;
  min-height: 45px;
}

.chat-user-info {
  display: flex;
  align-items: center;
}

.chat-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.chat-user-details {
  display: flex;
  flex-direction: column;
}

.chat-user-name {
  font-weight: 600;
  font-size: 16px;
  color: #212529;
  margin-bottom: 2px;
}

.chat-user-status {
  font-size: 13px;
  color: #28a745;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.chat-action-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.chat-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(74, 153, 248, 0.2), transparent);
  transition: left 0.5s;
}

.chat-action-btn:hover {
  background: rgba(74, 153, 248, 0.1);
  color: #4A99F8;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.3);
}

.chat-action-btn:hover::before {
  left: 100%;
}

.chat-action-btn:active {
  transform: translateY(0);
}

.messages-area {
  flex: 1;
  padding: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 6px;
  height: calc(100vh - 140px);
  justify-content: flex-start;
}

.message {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 70%;
}

.message-received {
  align-self: flex-start;
}

.message-sent {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-text {
  padding: 6px 10px;
  border-radius: 14px;
  font-size: 12px;
  line-height: 1.2;
  word-wrap: break-word;
}

.message-received .message-text {
  background-color: #f8f9fa;
  color: #212529;
  border-bottom-left-radius: 4px;
}

.message-sent .message-text {
  background-color: #4A99F8;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-time {
  font-size: 11px;
  color: #6c757d;
  padding: 0 4px;
}

.message-sent .message-time {
  text-align: right;
}

.message-input-container {
  padding: 8px 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 10px;
  align-items: flex-end;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  animation: fadeInUp 0.8s ease-out 0.7s both;
  min-height: 45px;
}

.message-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 8px 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-input-wrapper:focus-within {
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.3);
  border-color: #4A99F8;
  transform: translateY(-2px);
}

.message-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 8px 0;
  font-size: 14px;
  outline: none;
  resize: none;
}

.attachment-btn,
.emoji-btn,
.send-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: #6c757d;
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.attachment-btn::before,
.emoji-btn::before,
.send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(74, 153, 248, 0.2), transparent);
  transition: left 0.5s;
}

.attachment-btn:hover,
.emoji-btn:hover,
.send-btn:hover {
  background: rgba(74, 153, 248, 0.1);
  color: #4A99F8;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(74, 153, 248, 0.3);
}

.attachment-btn:hover::before,
.emoji-btn:hover::before,
.send-btn:hover::before {
  left: 100%;
}

.mic-btn {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #4A99F8 0%, #667eea 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(74, 153, 248, 0.4);
  position: relative;
  overflow: hidden;
}

.mic-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.mic-btn:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 32px rgba(74, 153, 248, 0.6);
}

.mic-btn:hover::before {
  left: 100%;
}

.mic-btn:active {
  transform: translateY(0) scale(1);
}

/* Scrollbar Styling */
.conversations-list::-webkit-scrollbar,
.messages-area::-webkit-scrollbar {
  width: 6px;
}

.conversations-list::-webkit-scrollbar-track,
.messages-area::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.conversations-list::-webkit-scrollbar-thumb,
.messages-area::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover,
.messages-area::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .messages-sidebar {
    display: none;
  }
  
  .conversations-panel {
    width: 100%;
  }
  
  .chat-panel {
    display: none;
  }
}

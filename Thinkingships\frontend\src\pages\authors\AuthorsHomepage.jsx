
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');
  const [currentBanner, setCurrentBanner] = useState(0);

  // Banner data for sliding banners
  const banners = [
    {
      id: 1,
      backgroundImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=600&fit=crop',
      title: 'Star wars jedi: survivor is',
      subtitle: 'nearly here - get hype!',
      author: {
        name: '<PERSON><PERSON><PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      }
    },
    {
      id: 2,
      backgroundImage: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1920&h=600&fit=crop',
      title: 'The Art of Modern',
      subtitle: 'Storytelling Techniques',
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      }
    },
    {
      id: 3,
      backgroundImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1920&h=600&fit=crop',
      title: 'Fantasy Worlds That',
      subtitle: 'Captivate Every Reader',
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      }
    },
    {
      id: 4,
      backgroundImage: 'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?w=1920&h=600&fit=crop',
      title: 'Poetry in Motion:',
      subtitle: 'Words That Dance',
      author: {
        name: 'Priya Sharma',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      }
    }
  ];

  // Tab-specific content data
  const storiesContent = [
    {
      id: 1,
      author: { name: 'Robert Fox', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor.',
      image: 'https://picsum.photos/800/400?random=10'
    },
    {
      id: 2,
      author: { name: 'Sarah Johnson', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists.',
      image: 'https://picsum.photos/800/400?random=20'
    },
    {
      id: 3,
      author: { name: 'Michael Chen', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities.',
      image: 'https://picsum.photos/800/400?random=30'
    }
  ];

  const poemsContent = [
    {
      id: 101,
      author: { name: 'Priya Sharma', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'Whispers of the Night',
      category: 'Poem',
      date: 'Mar 10, 2022',
      tags: ['#poetry', '#night', '#dreams'],
      excerpt: 'In the silence of the night, dreams take their flight, weaving stories in the moonlight.',
      image: 'https://picsum.photos/800/400?random=101'
    },
    {
      id: 102,
      author: { name: 'Amit Verma', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'Sunrise Symphony',
      category: 'Poem',
      date: 'Apr 2, 2022',
      tags: ['#sunrise', '#hope', '#poem'],
      excerpt: 'Golden rays awaken the earth, a new day sings its worth.',
      image: 'https://picsum.photos/800/400?random=102'
    }
  ];

  const blogsContent = [
    {
      id: 201,
      author: { name: 'Sneha Kapoor', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: '5 Tips for Productive Writing',
      category: 'Blog',
      date: 'May 5, 2023',
      tags: ['#writing', '#productivity', '#blog'],
      excerpt: 'Boost your writing productivity with these 5 simple tips that work for everyone.',
      image: 'https://picsum.photos/800/400?random=201'
    },
    {
      id: 202,
      author: { name: 'Rahul Singh', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'Why Reading Makes You a Better Writer',
      category: 'Blog',
      date: 'Jun 12, 2023',
      tags: ['#reading', '#writing', '#blog'],
      excerpt: 'Discover how reading regularly can transform your writing skills and creativity.',
      image: 'https://picsum.photos/800/400?random=202'
    }
  ];

  const ebooksContent = [
    {
      id: 301,
      author: { name: 'Karan Mehta', avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>' },
      title: 'The Digital Pen: eBook Revolution',
      category: 'e-Book',
      date: 'Jul 20, 2023',
      tags: ['#ebook', '#digital', '#reading'],
      excerpt: 'Explore how eBooks are changing the way we read and share stories in the digital age.',
      image: 'https://picsum.photos/800/400?random=301'
    }
  ];

  // Banner navigation functions
  const nextBanner = () => {
    setCurrentBanner((prev) => (prev + 1) % banners.length);
  };

  const prevBanner = () => {
    setCurrentBanner((prev) => (prev - 1 + banners.length) % banners.length);
  };

  const goToBanner = (index) => {
    setCurrentBanner(index);
  };

  let tabContent = storiesContent;
  if (activeTab === 'Poems') tabContent = poemsContent;
  else if (activeTab === 'Blogs') tabContent = blogsContent;
  else if (activeTab === 'e-books') tabContent = ebooksContent;

  return (
    <div className="w-full bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
      {/* Sliding Featured Banner */}
      <div className="relative w-full h-80 sm:h-96 lg:h-[400px] overflow-hidden">
        {/* Banner Slides Container */}
        <div
          className="flex transition-transform duration-500 ease-in-out h-full"
          style={{ transform: `translateX(-${currentBanner * 100}%)` }}
        >
          {banners.map((banner, index) => (
            <div
              key={banner.id}
              className="w-full h-full flex-shrink-0 bg-cover bg-center relative"
              style={{
                backgroundImage: `url('${banner.backgroundImage}')`
              }}
            >
              {/* Simple Overlay */}
              <div className="absolute inset-0 bg-black/50"></div>

              {/* Banner Content */}
              <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                <div className="max-w-6xl mx-auto px-4">
                  {/* Title */}
                  <div className="mb-4">
                    <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight text-white mb-2 underline decoration-2 underline-offset-4">
                      {banner.title}
                    </h1>
                    <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight text-white underline decoration-2 underline-offset-4">
                      {banner.subtitle}
                    </h2>
                  </div>

                  {/* Author Info */}
                  <div className="flex items-center gap-3 mb-4">
                    <img
                      src={banner.author.avatar}
                      alt={banner.author.name}
                      className="w-10 h-10 rounded-full border-2 border-white"
                    />
                    <div>
                      <span className="text-base font-semibold text-white">{banner.author.name}</span>
                    </div>
                  </div>

                  {/* Interaction Buttons - Like, Comment, Share */}
                  <div className="flex items-center gap-8 mb-3">
                    <button className="flex items-center gap-2 text-white hover:text-blue-300 transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <span className="font-semibold text-sm">Like</span>
                    </button>
                    <button className="flex items-center gap-2 text-white hover:text-green-300 transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <span className="font-semibold text-sm">Comment</span>
                    </button>
                    <button className="flex items-center gap-2 text-white hover:text-purple-300 transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                      </svg>
                      <span className="font-semibold text-sm">Share</span>
                    </button>
                  </div>

                  {/* Liked by section */}
                  <div className="flex items-center gap-2">
                    <div className="flex -space-x-1">
                      <div className="w-6 h-6 bg-blue-400 rounded-full border border-white"></div>
                      <div className="w-6 h-6 bg-green-400 rounded-full border border-white"></div>
                      <div className="w-6 h-6 bg-purple-400 rounded-full border border-white"></div>
                      <div className="w-6 h-6 bg-pink-400 rounded-full border border-white"></div>
                    </div>
                    <span className="text-white text-sm">Liked by and 1.5K others</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        <button
          onClick={prevBanner}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-all duration-300 backdrop-blur-sm"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <button
          onClick={nextBanner}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-all duration-300 backdrop-blur-sm"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>

        {/* Dots Indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
          {banners.map((_, index) => (
            <button
              key={index}
              onClick={() => goToBanner(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                currentBanner === index
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/70'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Blue Background Line - Same width as banner */}
      <div className="w-full bg-blue-500 py-1 shadow-lg">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex justify-center items-center gap-6">
            <button
              onClick={() => setActiveTab('Stories')}
              className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                activeTab === 'Stories'
                  ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                  : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
              }`}
            >
              <span className="text-lg">📚</span>
              <span className="text-base">Stories</span>
            </button>

            <button
              onClick={() => setActiveTab('Poems')}
              className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                activeTab === 'Poems'
                  ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                  : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
              }`}
            >
              <span className="text-lg">🎵</span>
              <span className="text-base">Poems</span>
            </button>

            <button
              onClick={() => setActiveTab('Blogs')}
              className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                activeTab === 'Blogs'
                  ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                  : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
              }`}
            >
              <span className="text-lg">📝</span>
              <span className="text-base">Blogs</span>
            </button>

            <button
              onClick={() => setActiveTab('e-books')}
              className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                activeTab === 'e-books'
                  ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                  : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
              }`}
            >
              <span className="text-lg">📖</span>
              <span className="text-base">e-books</span>
            </button>
          </div>
        </div>
      </div>

      {/* Ultra-Attractive Main Content */}
      <div className="bg-gradient-to-br from-white via-blue-50 to-indigo-100 min-h-screen relative overflow-hidden">
        {/* Enhanced Background Patterns */}
        <div className="absolute inset-0 z-0 opacity-10">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-gradient-to-br from-indigo-400 to-purple-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
          <div className="absolute top-10 left-10 w-64 h-64 bg-gradient-to-br from-cyan-400 to-blue-400 rounded-full blur-3xl animate-pulse delay-3000"></div>
          <div className="absolute bottom-10 right-10 w-72 h-72 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full blur-3xl animate-pulse delay-4000"></div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-2 h-2 bg-blue-300/40 rounded-full animate-ping"></div>
          <div className="absolute top-40 right-40 w-1.5 h-1.5 bg-purple-300/40 rounded-full animate-ping delay-1000"></div>
          <div className="absolute bottom-40 left-60 w-2 h-2 bg-pink-300/40 rounded-full animate-ping delay-2000"></div>
          <div className="absolute bottom-60 right-20 w-1 h-1 bg-cyan-300/40 rounded-full animate-ping delay-3000"></div>
          <div className="absolute top-60 left-1/3 w-1.5 h-1.5 bg-indigo-300/40 rounded-full animate-ping delay-4000"></div>
          <div className="absolute bottom-20 left-1/2 w-1 h-1 bg-rose-300/40 rounded-full animate-ping delay-5000"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto px-4 pb-0">
          {/* Main Content Area */}
          <div>
            {/* Advanced Content List with Scroller */}
            <div className="h-screen overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-blue-400 scrollbar-track-gray-200">

              {/* Sort By Section inside scroll area */}
              <div className="mb-6 flex justify-between items-center bg-white p-4 rounded-lg shadow-sm">
                <div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-1">
                    {activeTab === 'Stories' && 'Discover Amazing Stories'}
                    {activeTab === 'Poems' && 'Discover Amazing Poems'}
                    {activeTab === 'Blogs' && 'Discover Amazing Blogs'}
                    {activeTab === 'e-books' && 'Discover Amazing e-Books'}
                  </h2>
                  <p className="text-gray-600 text-sm">Explore the best content from talented authors</p>
                </div>
                <div className="flex items-center gap-3 bg-white/90 backdrop-blur-md px-4 py-2 rounded-xl shadow-md border border-gray-200/50">
                  <span className="text-gray-700 font-semibold text-sm">Sort by</span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg px-3 py-1.5 text-gray-800 font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm"
                  >
                    <option value="Most Read">Most Read</option>
                    <option value="Newest">Newest</option>
                    <option value="Oldest">Oldest</option>
                    <option value="Most Liked">Most Liked</option>
                  </select>
                </div>
              </div>

              {/* Content Items */}
              <div className="space-y-8">
            {tabContent.map((item) => (
              <div
                key={item.id}
                className="group bg-white/95 backdrop-blur-md border border-gray-200/50 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden transform hover:-translate-y-2 hover:scale-[1.02]"
              >
                {/* Advanced Decorative Background Elements */}
                <div className="absolute -right-16 -top-16 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-indigo-200/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse blur-xl"></div>
                <div className="absolute -left-16 -bottom-16 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100 animate-pulse blur-xl"></div>

                {/* Floating Particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-60 transition-opacity duration-1000">
                  <div className="absolute top-8 right-8 w-2 h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-ping shadow-lg"></div>
                  <div className="absolute top-16 left-16 w-1.5 h-1.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-ping delay-500 shadow-lg"></div>
                  <div className="absolute bottom-16 right-16 w-2 h-2 bg-gradient-to-r from-indigo-400 to-blue-400 rounded-full animate-ping delay-1000 shadow-lg"></div>
                </div>

                {/* Content Container */}
                <div className="relative z-10">
                  {/* Author Header - Enhanced */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <img
                        src={item.author.avatar}
                        alt={item.author.name}
                        className="w-12 h-12 rounded-full border-2 border-blue-300 shadow-md object-cover"
                      />
                      <span className="font-bold text-gray-800 text-lg group-hover:text-blue-700 transition-colors duration-300">{item.author.name}</span>
                      <span className="text-blue-600 text-xs font-medium bg-blue-50 px-2 py-1 rounded-full ml-2">Author</span>
                    </div>
                    <button className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-xl hover:shadow-blue-500/50">
                      Follow
                    </button>
                  </div>

                  {/* Content Title - Enhanced */}
                  <h2 className="text-xl font-bold text-gray-800 mb-4 leading-tight group-hover:text-blue-700 transition-colors duration-300">
                    {item.title}
                  </h2>

                  {/* Category and Date - Enhanced */}
                  <div className="flex items-center gap-4 mb-4">
                    <span className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium border border-blue-100 shadow-sm">
                      {item.category}
                    </span>
                    <span className="bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-3 py-1 rounded-full text-sm border border-gray-200 shadow-sm">
                      {item.date}
                    </span>
                  </div>

                  {/* Tags - Enhanced */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="text-blue-600 text-sm hover:text-blue-800 cursor-pointer transition-colors duration-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Excerpt - Enhanced */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-4 group-hover:text-gray-700 transition-colors duration-300">
                    {item.excerpt}
                  </p>

                  {/* Content Image - Enhanced */}
                  <div className="relative rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500 transform group-hover:scale-[1.02]">
                    <img
                      src={item.image}
                      alt="Content preview"
                      className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
                      onError={(e) => {
                        // First fallback - try a different image
                        if (!e.target.dataset.fallback) {
                          e.target.dataset.fallback = "1";
                          const fallbackImages = {
                            'Romance': 'https://picsum.photos/800/400?random=1',
                            'Mystery': 'https://picsum.photos/800/400?random=2',
                            'Fantasy': 'https://picsum.photos/800/400?random=3',
                            'Science': 'https://picsum.photos/800/400?random=4'
                          };
                          e.target.src = fallbackImages[item.category] || 'https://picsum.photos/800/400?random=5';
                        } else {
                          // Final fallback - colored placeholder
                          e.target.src = `https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=${encodeURIComponent(item.category)}`;
                        }
                      }}
                      loading="lazy"
                    />
                  </div>

                  {/* Action Icons */}
                  <div className="mt-4 flex justify-around items-center border-t border-gray-200 pt-4">
                    <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500 transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <span className="font-semibold text-sm">Like</span>
                    </button>
                    <button className="flex items-center gap-2 text-gray-600 hover:text-green-500 transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <span className="font-semibold text-sm">Comment</span>
                    </button>
                    <button className="flex items-center gap-2 text-gray-600 hover:text-purple-500 transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                      </svg>
                      <span className="font-semibold text-sm">Share</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
